'use client'

import {useSelectedSport} from '@/context/SelectedSportContext'
import {Sport} from '@/types/sports'
import Image from 'next/image'
import React from 'react'
import styles from './Sports.module.css'

interface SportsProps {
    sports: Sport[]
}

export const Sports = React.memo(({sports}: SportsProps) => {
    const {selectedSport, setSelectedSport} = useSelectedSport()
    const handleClick = (sport: Sport) => {
        // Clear scroll position when switching sports
        sessionStorage.removeItem('highlights-scroll-position')
        setSelectedSport(sport.label)
    }

    return (
        <ul className={styles.sportsList}>
            {sports.map((sport) => (
                <a key={sport.id} href={`/${sport.label}`} onClick={() => handleClick(sport)}>
                    <li className={selectedSport === sport.label ? styles.selected : ''}>
                        <Image
                            src={`/sports/${sport.label}.png`}
                            width={24}
                            height={24}
                            alt={sport.name}
                        />
                    </li>
                </a>
            ))}
        </ul>
    )
})
